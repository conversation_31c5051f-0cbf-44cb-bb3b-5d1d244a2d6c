import React, { useState } from "react";
import { FaMobile } from "react-icons/fa6";
import "../../styles/CheckoutPage.css";

const CheckoutPage = () => {
  const [formData, setFormData] = useState({
    phone: "",
    countryCode: "+91",
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const handleCountryCodeChange = (e) => {
    setFormData({
      ...formData,
      countryCode: e.target.value,
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = "Phone number must be 10 digits";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Handle form submission logic here
    console.log("Form submitted:", formData);
  };
  return (
    <div className="checkout-page">
      {/* Main Content */}
      <div className="max-container">
        <div className="checkout-content">
          {/* Left Section - Checkout Form */}
          <div className="checkout-left">
            <div className="checkout-form-container">
              <h1 className="checkout-title">Checkout</h1>

              <div className="leftborderdiv">
                {/* Alert Message */}
                <div className="checkout-alert">
                  Please log in to purchase the content!
                </div>

                {/* Sign In Section */}
                <div className="signin-section">
                  <div className="signin-sectioncontainer">
                    <h2 className="signin-title">Sign In</h2>
                    <p className="signin-subtitle">
                      Don't have an account?{" "}
                      <a href="/signup" className="signup-link">
                        Sign Up
                      </a>
                    </p>
                  </div>

                  <form onSubmit={handleSubmit} className="signin-form">
                    <div className="auth-form-input form-input-container">
                      <div className="phone-input-wrapper">
                        <div>
                          <div className="country-code-select">
                            <FaMobile style={{ color: "var(--dark-gray)" }} />
                            <select
                              value={formData.countryCode}
                              onChange={handleCountryCodeChange}
                              className="selectstylesnone"
                            >
                              <option value="+91">+91</option>
                              <option value="+1">+1</option>
                              <option value="+44">+44</option>
                              <option value="+61">+61</option>
                              <option value="+86">+86</option>
                              <option value="+49">+49</option>
                              <option value="+33">+33</option>
                              <option value="+81">+81</option>
                              <option value="+7">+7</option>
                              <option value="+55">+55</option>
                            </select>
                          </div>
                        </div>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={(e) => {
                            const phoneValue = e.target.value.replace(/\D/g, "");
                            handleChange({
                              target: {
                                name: "phone",
                                value: phoneValue,
                              },
                            });
                          }}
                          placeholder="Enter Phone Number"
                          className={`form-input phone-input ${
                            errors.phone ? "input-error" : ""
                          }`}
                          required
                          pattern="[0-9]*"
                        />
                      </div>
                      {errors.phone && <p className="error-message">{errors.phone}</p>}
                    </div>

                    <button type="submit" className="btn-primary">
                      Send Verification Code
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>

          {/* Right Section - Order Summary */}
          <div className="checkout-right">
            <div className="order-summary">
              <h2 className="order-title">Your Order</h2>

              {/* Item Info */}
              <div className="item-info-section">
                <h3 className="item-info-title">Item Info</h3>

                <div className="item-details">
                  <div className="item-image">
                    <img
                      src="https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"
                      alt=""
                      className="item-thumbnail"
                    />
                  </div>

                  <div className="item-description">
                    <h4 className="item-name">
                      Frank Martin - Drills and Coaching Philosophy to
                      Developing Toughness in Basketball
                    </h4>
                  </div>
                </div>
              </div>

              {/* Pricing */}
              <div className="pricing-section">
                <div className="price-row">
                  <span className="price-label">Subtotal</span>
                  <span className="price-value">$22.00</span>
                </div>

                <div className="price-row total-row">
                  <span className="price-label">Total</span>
                  <span className="price-value">$22.00</span>
                </div>
              </div>

              {/* Place Order Button */}
              <button className="place-order-btn btn-primary">
                Place Order & Download
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
